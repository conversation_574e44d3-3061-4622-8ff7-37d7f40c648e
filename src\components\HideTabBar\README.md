# Hide Tab Bar Component

Thư viện component để ẩn/hiện bottom tab bar khi scroll FlatList trong React Native.

## Cách sử dụng

### 1. Wrap ứng dụng với BottomTabWrapper

```tsx
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { BottomTabWrapper, AnimatedTabBar } from './src/components';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <BottomTabWrapper>
        <Tab.Navigator
          tabBar={(props) => <AnimatedTabBar {...props} />}
        >
          <Tab.Screen name="Home" component={HomeScreen} />
          <Tab.Screen name="Profile" component={ProfileScreen} />
        </Tab.Navigator>
      </BottomTabWrapper>
    </NavigationContainer>
  );
}
```

### 2. Sử dụng hook trong screen có FlatList

```tsx
import React from 'react';
import { FlatList, View, Text } from 'react-native';
import { useHideTabBarOnScroll } from './src/components';

const HomeScreen = () => {
  const { onScroll } = useHideTabBarOnScroll();

  const data = Array.from({ length: 50 }, (_, i) => ({ id: i, title: `Item ${i}` }));

  const renderItem = ({ item }) => (
    <View style={{ padding: 20, borderBottomWidth: 1, borderBottomColor: '#eee' }}>
      <Text>{item.title}</Text>
    </View>
  );

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      onScroll={onScroll}
      scrollEventThrottle={16}
    />
  );
};
```

## API

### BottomTabWrapper
Component wrapper cung cấp context cho việc điều khiển tab bar.

**Props:**
- `children: ReactNode` - Các component con

### useHideTabBarOnScroll
Hook để xử lý sự kiện scroll và điều khiển tab bar.

**Returns:**
- `onScroll: (event: NativeSyntheticEvent<NativeScrollEvent>) => void` - Function để gắn vào FlatList

### AnimatedTabBar
Component tab bar có animation ẩn/hiện.

**Props:**
- Tất cả props của `BottomTabBarProps` từ `@react-navigation/bottom-tabs`

## Tùy chỉnh

Bạn có thể tùy chỉnh animation bằng cách chỉnh sửa các giá trị trong `BottomTabWrapper.tsx`:

- `toValue`: Khoảng cách di chuyển (mặc định: 100)
- `duration`: Thời gian animation (mặc định: 200ms)
- Threshold scroll: Khoảng cách scroll tối thiểu để trigger animation (mặc định: 50px)
