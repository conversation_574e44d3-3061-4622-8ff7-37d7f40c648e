# Hướng dẫn tích hợp Hide Tab Bar

## Bước 1: Cậ<PERSON> nhật Navigator ch<PERSON><PERSON> file navigator ch<PERSON><PERSON> c<PERSON> b<PERSON> (thường là `src/navigators/index.tsx`), wrap toàn bộ navigator với `BottomTabWrapper`:

```tsx
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { BottomTabWrapper, AnimatedTabBar } from '../components';

const Tab = createBottomTabNavigator();

export const AppNavigator = () => {
  return (
    <NavigationContainer>
      <BottomTabWrapper
        animationConfig={{
          duration: 250,
          hideValue: 100,
          showValue: 0,
        }}
      >
        <Tab.Navigator
          tabBar={(props) => <AnimatedTabBar {...props} />}
          screenOptions={{
            headerShown: false,
          }}
        >
          <Tab.Screen name="Home" component={HomeScreen} />
          <Tab.Screen name="Search" component={SearchScreen} />
          <Tab.Screen name="Profile" component={ProfileScreen} />
        </Tab.Navigator>
      </BottomTabWrapper>
    </NavigationContainer>
  );
};
```

## Bước 2: Sử dụng trong Screen

Trong các screen có FlatList hoặc ScrollView:

```tsx
import React from 'react';
import { FlatList, View } from 'react-native';
import { useHideTabBarOnScroll } from '../components';

export const HomeScreen = () => {
  const { onScroll } = useHideTabBarOnScroll({
    scrollThreshold: 50, // Tùy chỉnh ngưỡng scroll
  });

  return (
    <View style={{ flex: 1 }}>
      <FlatList
        data={yourData}
        renderItem={yourRenderItem}
        onScroll={onScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: 100, // Quan trọng: để tránh bị che bởi tab bar
        }}
      />
    </View>
  );
};
```

## Bước 3: Tùy chỉnh Tab Bar (Tùy chọn)

Nếu bạn muốn tùy chỉnh giao diện tab bar, bạn có thể tạo component riêng:

```tsx
import React, { useContext } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Animated } from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { TabBarVisibilityContext } from '../components';

export const CustomTabBar: React.FC<BottomTabBarProps> = ({ state, descriptors, navigation }) => {
  const context = useContext(TabBarVisibilityContext);
  
  if (!context) return null;

  const { tabBarTranslateY } = context;

  return (
    <Animated.View
      style={[
        styles.tabBar,
        {
          transform: [{ translateY: tabBarTranslateY }],
        },
      ]}
    >
      {/* Render tab items */}
    </Animated.View>
  );
};
```

## Cấu hình Animation

Bạn có thể tùy chỉnh animation thông qua `animationConfig`:

```tsx
<BottomTabWrapper
  animationConfig={{
    duration: 300,        // Thời gian animation (ms)
    hideValue: 120,       // Khoảng cách ẩn tab bar
    showValue: 0,         // Vị trí hiển thị tab bar
  }}
>
```

## Cấu hình Scroll Threshold

Tùy chỉnh ngưỡng scroll để trigger animation:

```tsx
const { onScroll } = useHideTabBarOnScroll({
  scrollThreshold: 30, // Scroll 30px mới trigger animation
});
```

## Lưu ý quan trọng

1. **Padding Bottom**: Luôn thêm `paddingBottom` cho `contentContainerStyle` của FlatList để tránh nội dung bị che bởi tab bar.

2. **ScrollEventThrottle**: Đặt `scrollEventThrottle={16}` để animation mượt mà.

3. **UseNativeDriver**: Animation sử dụng native driver để có hiệu suất tốt nhất.

4. **Context Provider**: Đảm bảo tất cả screen sử dụng hook phải nằm trong `BottomTabWrapper`.

## Troubleshooting

### Tab bar không ẩn/hiện
- Kiểm tra xem screen có được wrap trong `BottomTabWrapper` không
- Đảm bảo `onScroll` được gắn vào FlatList/ScrollView
- Kiểm tra `scrollEventThrottle` được đặt

### Animation không mượt
- Đặt `scrollEventThrottle={16}`
- Giảm `scrollThreshold` xuống
- Kiểm tra hiệu suất của `renderItem`

### Nội dung bị che
- Thêm `paddingBottom` cho `contentContainerStyle`
- Điều chỉnh `hideValue` trong `animationConfig`
