import {Topic} from '../types';

export const vocabularyData: Topic[] = [
  {
    id: 'fruits',
    name: 'Fruits',
    nameVietnamese: 'Trái <PERSON>',
    description: 'Học từ vựng về các loại trái cây',
    icon: '🍎',
    color: '#FF6B6B',
    words: [
      {
        id: 'apple',
        english: 'Apple',
        vietnamese: 'Táo',
        phonetic: '/ˈæpəl/',
        example: 'I eat an apple every day.',
        topicId: 'fruits',
      },
      {
        id: 'banana',
        english: 'Banana',
        vietnamese: 'Chuối',
        phonetic: '/bəˈnænə/',
        example: 'The banana is yellow.',
        topicId: 'fruits',
      },
      {
        id: 'orange',
        english: 'Orange',
        vietnamese: 'Cam',
        phonetic: '/ˈɔːrɪndʒ/',
        example: 'Orange juice is delicious.',
        topicId: 'fruits',
      },
      {
        id: 'grape',
        english: 'Grape',
        vietnamese: 'Nho',
        phonetic: '/ɡreɪp/',
        example: 'Grapes are sweet.',
        topicId: 'fruits',
      },
      {
        id: 'strawberry',
        english: 'Strawberry',
        vietnamese: 'Dâu tây',
        phonetic: '/ˈstrɔːberi/',
        example: 'Strawberries are red.',
        topicId: 'fruits',
      },
      {
        id: 'mango',
        english: 'Mango',
        vietnamese: 'Xoài',
        phonetic: '/ˈmæŋɡoʊ/',
        example: 'Mango is a tropical fruit.',
        topicId: 'fruits',
      },
      {
        id: 'pineapple',
        english: 'Pineapple',
        vietnamese: 'Dứa/Thơm',
        phonetic: '/ˈpaɪnæpəl/',
        example: 'Pineapple is sweet and sour.',
        topicId: 'fruits',
      },
      {
        id: 'watermelon',
        english: 'Watermelon',
        vietnamese: 'Dưa hấu',
        phonetic: '/ˈwɔːtərmelən/',
        example: 'Watermelon is refreshing in summer.',
        topicId: 'fruits',
      },
      {
        id: 'coconut',
        english: 'Coconut',
        vietnamese: 'Dừa',
        phonetic: '/ˈkoʊkənʌt/',
        example: 'Coconut water is healthy.',
        topicId: 'fruits',
      },
      {
        id: 'lemon',
        english: 'Lemon',
        vietnamese: 'Chanh vàng',
        phonetic: '/ˈlemən/',
        example: 'Lemon is sour.',
        topicId: 'fruits',
      },
      {
        id: 'peach',
        english: 'Peach',
        vietnamese: 'Đào',
        phonetic: '/piːtʃ/',
        example: 'Peach is soft and sweet.',
        topicId: 'fruits',
      },
      {
        id: 'kiwi',
        english: 'Kiwi',
        vietnamese: 'Kiwi',
        phonetic: '/ˈkiːwi/',
        example: 'Kiwi is green inside.',
        topicId: 'fruits',
      },
      {
        id: 'cherry',
        english: 'Cherry',
        vietnamese: 'Anh đào',
        phonetic: '/ˈtʃeri/',
        example: 'Cherries are small and red.',
        topicId: 'fruits',
      },
      {
        id: 'plum',
        english: 'Plum',
        vietnamese: 'Mận',
        phonetic: '/plʌm/',
        example: 'Plums are purple.',
        topicId: 'fruits',
      },
      {
        id: 'avocado',
        english: 'Avocado',
        vietnamese: 'Bơ',
        phonetic: '/ˌævəˈkɑːdoʊ/',
        example: 'Avocado is healthy.',
        topicId: 'fruits',
      },
      {
        id: 'papaya',
        english: 'Papaya',
        vietnamese: 'Đu đủ',
        phonetic: '/pəˈpaɪə/',
        example: 'Papaya is orange.',
        topicId: 'fruits',
      },
    ],
  },
  {
    id: 'animals',
    name: 'Animals',
    nameVietnamese: 'Động Vật',
    description: 'Học từ vựng về các loài động vật',
    icon: '🐶',
    color: '#4ECDC4',
    words: [
      {
        id: 'dog',
        english: 'Dog',
        vietnamese: 'Chó',
        phonetic: '/dɔːɡ/',
        example: 'The dog is friendly.',
        topicId: 'animals',
      },
      {
        id: 'cat',
        english: 'Cat',
        vietnamese: 'Mèo',
        phonetic: '/kæt/',
        example: 'The cat is sleeping.',
        topicId: 'animals',
      },
      {
        id: 'bird',
        english: 'Bird',
        vietnamese: 'Chim',
        phonetic: '/bɜːrd/',
        example: 'The bird can fly.',
        topicId: 'animals',
      },
      {
        id: 'fish',
        english: 'Fish',
        vietnamese: 'Cá',
        phonetic: '/fɪʃ/',
        example: 'Fish live in water.',
        topicId: 'animals',
      },
      {
        id: 'elephant',
        english: 'Elephant',
        vietnamese: 'Voi',
        phonetic: '/ˈeləfənt/',
        example: 'The elephant is big.',
        topicId: 'animals',
      },
      {
        id: 'lion',
        english: 'Lion',
        vietnamese: 'Sư tử',
        phonetic: '/ˈlaɪən/',
        example: 'The lion is the king of animals.',
        topicId: 'animals',
      },
      {
        id: 'tiger',
        english: 'Tiger',
        vietnamese: 'Hổ',
        phonetic: '/ˈtaɪɡər/',
        example: 'Tigers have orange fur with black stripes.',
        topicId: 'animals',
      },
      {
        id: 'monkey',
        english: 'Monkey',
        vietnamese: 'Khỉ',
        phonetic: '/ˈmʌŋki/',
        example: 'Monkeys like to eat bananas.',
        topicId: 'animals',
      },
      {
        id: 'rabbit',
        english: 'Rabbit',
        vietnamese: 'Thỏ',
        phonetic: '/ˈræbɪt/',
        example: 'Rabbits have long ears.',
        topicId: 'animals',
      },
      {
        id: 'horse',
        english: 'Horse',
        vietnamese: 'Ngựa',
        phonetic: '/hɔːrs/',
        example: 'Horses can run very fast.',
        topicId: 'animals',
      },
      {
        id: 'cow',
        english: 'Cow',
        vietnamese: 'Bò',
        phonetic: '/kaʊ/',
        example: 'Cows give us milk.',
        topicId: 'animals',
      },
      {
        id: 'pig',
        english: 'Pig',
        vietnamese: 'Heo/Lợn',
        phonetic: '/pɪɡ/',
        example: 'Pigs are intelligent animals.',
        topicId: 'animals',
      },
      {
        id: 'sheep',
        english: 'Sheep',
        vietnamese: 'Cừu',
        phonetic: '/ʃiːp/',
        example: 'Sheep give us wool.',
        topicId: 'animals',
      },
      {
        id: 'goat',
        english: 'Goat',
        vietnamese: 'Dê',
        phonetic: '/ɡoʊt/',
        example: 'Goats eat grass.',
        topicId: 'animals',
      },
      {
        id: 'duck',
        english: 'Duck',
        vietnamese: 'Vịt',
        phonetic: '/dʌk/',
        example: 'Ducks swim in the pond.',
        topicId: 'animals',
      },
      {
        id: 'chicken_animal',
        english: 'Chicken',
        vietnamese: 'Gà',
        phonetic: '/ˈtʃɪkɪn/',
        example: 'Chickens lay eggs.',
        topicId: 'animals',
      },
      {
        id: 'frog',
        english: 'Frog',
        vietnamese: 'Ếch',
        phonetic: '/frɔːɡ/',
        example: 'Frogs live near water.',
        topicId: 'animals',
      },
      {
        id: 'snake',
        english: 'Snake',
        vietnamese: 'Rắn',
        phonetic: '/sneɪk/',
        example: 'Snakes have no legs.',
        topicId: 'animals',
      },
    ],
  },
  {
    id: 'colors',
    name: 'Colors',
    nameVietnamese: 'Màu Sắc',
    description: 'Học từ vựng về các màu sắc',
    icon: '🌈',
    color: '#FFE66D',
    words: [
      {
        id: 'red',
        english: 'Red',
        vietnamese: 'Đỏ',
        phonetic: '/red/',
        example: 'The rose is red.',
        topicId: 'colors',
      },
      {
        id: 'blue',
        english: 'Blue',
        vietnamese: 'Xanh dương',
        phonetic: '/bluː/',
        example: 'The sky is blue.',
        topicId: 'colors',
      },
      {
        id: 'green',
        english: 'Green',
        vietnamese: 'Xanh lá',
        phonetic: '/ɡriːn/',
        example: 'Grass is green.',
        topicId: 'colors',
      },
      {
        id: 'yellow',
        english: 'Yellow',
        vietnamese: 'Vàng',
        phonetic: '/ˈjeloʊ/',
        example: 'The sun is yellow.',
        topicId: 'colors',
      },
      {
        id: 'purple',
        english: 'Purple',
        vietnamese: 'Tím',
        phonetic: '/ˈpɜːrpəl/',
        example: 'The flower is purple.',
        topicId: 'colors',
      },
      {
        id: 'orange',
        english: 'Orange',
        vietnamese: 'Cam (màu)',
        phonetic: '/ˈɔːrɪndʒ/',
        example: 'Orange is a warm color.',
        topicId: 'colors',
      },
      {
        id: 'pink',
        english: 'Pink',
        vietnamese: 'Hồng',
        phonetic: '/pɪŋk/',
        example: 'Pink roses are beautiful.',
        topicId: 'colors',
      },
      {
        id: 'brown',
        english: 'Brown',
        vietnamese: 'Nâu',
        phonetic: '/braʊn/',
        example: 'The tree trunk is brown.',
        topicId: 'colors',
      },
      {
        id: 'black',
        english: 'Black',
        vietnamese: 'Đen',
        phonetic: '/blæk/',
        example: 'The night sky is black.',
        topicId: 'colors',
      },
      {
        id: 'white',
        english: 'White',
        vietnamese: 'Trắng',
        phonetic: '/waɪt/',
        example: 'Snow is white.',
        topicId: 'colors',
      },
      {
        id: 'gray',
        english: 'Gray',
        vietnamese: 'Xám',
        phonetic: '/ɡreɪ/',
        example: 'Clouds are gray before rain.',
        topicId: 'colors',
      },
    ],
  },
  {
    id: 'family',
    name: 'Family',
    nameVietnamese: 'Gia Đình',
    description: 'Học từ vựng về các thành viên trong gia đình',
    icon: '👨‍👩‍👧‍👦',
    color: '#FF8E53',
    words: [
      {
        id: 'father',
        english: 'Father',
        vietnamese: 'Bố',
        phonetic: '/ˈfɑːðər/',
        example: 'My father is tall.',
        topicId: 'family',
      },
      {
        id: 'mother',
        english: 'Mother',
        vietnamese: 'Mẹ',
        phonetic: '/ˈmʌðər/',
        example: 'My mother is kind.',
        topicId: 'family',
      },
      {
        id: 'brother',
        english: 'Brother',
        vietnamese: 'Anh/Em trai',
        phonetic: '/ˈbrʌðər/',
        example: 'I have one brother.',
        topicId: 'family',
      },
      {
        id: 'sister',
        english: 'Sister',
        vietnamese: 'Chị/Em gái',
        phonetic: '/ˈsɪstər/',
        example: 'My sister is smart.',
        topicId: 'family',
      },
      {
        id: 'grandmother',
        english: 'Grandmother',
        vietnamese: 'Bà',
        phonetic: '/ˈɡrænmʌðər/',
        example: 'Grandmother tells stories.',
        topicId: 'family',
      },
      {
        id: 'grandfather',
        english: 'Grandfather',
        vietnamese: 'Ông',
        phonetic: '/ˈɡrænfɑːðər/',
        example: 'Grandfather reads newspapers.',
        topicId: 'family',
      },
      {
        id: 'uncle',
        english: 'Uncle',
        vietnamese: 'Chú/Bác',
        phonetic: '/ˈʌŋkəl/',
        example: 'My uncle is funny.',
        topicId: 'family',
      },
      {
        id: 'aunt',
        english: 'Aunt',
        vietnamese: 'Cô/Dì',
        phonetic: '/ænt/',
        example: 'Aunt Mary cooks well.',
        topicId: 'family',
      },
      {
        id: 'cousin',
        english: 'Cousin',
        vietnamese: 'Anh/Chị/Em họ',
        phonetic: '/ˈkʌzən/',
        example: 'My cousin lives far away.',
        topicId: 'family',
      },
      {
        id: 'son',
        english: 'Son',
        vietnamese: 'Con trai',
        phonetic: '/sʌn/',
        example: 'Their son is very smart.',
        topicId: 'family',
      },
      {
        id: 'daughter',
        english: 'Daughter',
        vietnamese: 'Con gái',
        phonetic: '/ˈdɔːtər/',
        example: 'Their daughter loves music.',
        topicId: 'family',
      },
    ],
  },
  {
    id: 'jobs',
    name: 'Jobs',
    nameVietnamese: 'Nghề Nghiệp',
    description: 'Học từ vựng về các nghề nghiệp',
    icon: '👨‍💼',
    color: '#A8E6CF',
    words: [
      {
        id: 'teacher',
        english: 'Teacher',
        vietnamese: 'Giáo viên',
        phonetic: '/ˈtiːtʃər/',
        example: 'The teacher is helpful.',
        topicId: 'jobs',
      },
      {
        id: 'doctor',
        english: 'Doctor',
        vietnamese: 'Bác sĩ',
        phonetic: '/ˈdɑːktər/',
        example: 'The doctor helps people.',
        topicId: 'jobs',
      },
      {
        id: 'nurse',
        english: 'Nurse',
        vietnamese: 'Y tá',
        phonetic: '/nɜːrs/',
        example: 'The nurse is caring.',
        topicId: 'jobs',
      },
      {
        id: 'police',
        english: 'Police Officer',
        vietnamese: 'Cảnh sát',
        phonetic: '/pəˈliːs ˈɔːfɪsər/',
        example: 'Police officers keep us safe.',
        topicId: 'jobs',
      },
      {
        id: 'chef',
        english: 'Chef',
        vietnamese: 'Đầu bếp',
        phonetic: '/ʃef/',
        example: 'The chef cooks delicious food.',
        topicId: 'jobs',
      },
      {
        id: 'engineer',
        english: 'Engineer',
        vietnamese: 'Kỹ sư',
        phonetic: '/ˌendʒɪˈnɪr/',
        example: 'Engineers design buildings.',
        topicId: 'jobs',
      },
      {
        id: 'lawyer',
        english: 'Lawyer',
        vietnamese: 'Luật sư',
        phonetic: '/ˈlɔːjər/',
        example: 'Lawyers work in courts.',
        topicId: 'jobs',
      },
      {
        id: 'pilot',
        english: 'Pilot',
        vietnamese: 'Phi công',
        phonetic: '/ˈpaɪlət/',
        example: 'Pilots fly airplanes.',
        topicId: 'jobs',
      },
      {
        id: 'farmer',
        english: 'Farmer',
        vietnamese: 'Nông dân',
        phonetic: '/ˈfɑːrmər/',
        example: 'Farmers grow crops.',
        topicId: 'jobs',
      },
      {
        id: 'artist',
        english: 'Artist',
        vietnamese: 'Nghệ sĩ',
        phonetic: '/ˈɑːrtɪst/',
        example: 'Artists create beautiful paintings.',
        topicId: 'jobs',
      },
      {
        id: 'musician',
        english: 'Musician',
        vietnamese: 'Nhạc sĩ',
        phonetic: '/mjuˈzɪʃən/',
        example: 'Musicians play instruments.',
        topicId: 'jobs',
      },
      {
        id: 'driver',
        english: 'Driver',
        vietnamese: 'Tài xế',
        phonetic: '/ˈdraɪvər/',
        example: 'Bus drivers transport people.',
        topicId: 'jobs',
      },
    ],
  },
  {
    id: 'food',
    name: 'Food',
    nameVietnamese: 'Thức Ăn',
    description: 'Học từ vựng về các món ăn và thức uống',
    icon: '🍕',
    color: '#FF9F43',
    words: [
      {
        id: 'rice',
        english: 'Rice',
        vietnamese: 'Cơm',
        phonetic: '/raɪs/',
        example: 'I eat rice every day.',
        topicId: 'food',
      },
      {
        id: 'bread',
        english: 'Bread',
        vietnamese: 'Bánh mì',
        phonetic: '/bred/',
        example: 'I have bread for breakfast.',
        topicId: 'food',
      },
      {
        id: 'noodles',
        english: 'Noodles',
        vietnamese: 'Mì',
        phonetic: '/ˈnuːdəlz/',
        example: 'Vietnamese noodles are delicious.',
        topicId: 'food',
      },
      {
        id: 'soup',
        english: 'Soup',
        vietnamese: 'Súp/Canh',
        phonetic: '/suːp/',
        example: 'Hot soup is good for cold weather.',
        topicId: 'food',
      },
      {
        id: 'chicken',
        english: 'Chicken',
        vietnamese: 'Thịt gà',
        phonetic: '/ˈtʃɪkɪn/',
        example: 'Grilled chicken is healthy.',
        topicId: 'food',
      },
      {
        id: 'beef',
        english: 'Beef',
        vietnamese: 'Thịt bò',
        phonetic: '/biːf/',
        example: 'Beef steak is expensive.',
        topicId: 'food',
      },
      {
        id: 'pork',
        english: 'Pork',
        vietnamese: 'Thịt heo',
        phonetic: '/pɔːrk/',
        example: 'Pork is popular in Vietnamese cuisine.',
        topicId: 'food',
      },
      {
        id: 'vegetables',
        english: 'Vegetables',
        vietnamese: 'Rau củ',
        phonetic: '/ˈvedʒtəbəlz/',
        example: 'Vegetables are good for health.',
        topicId: 'food',
      },
      {
        id: 'egg',
        english: 'Egg',
        vietnamese: 'Trứng',
        phonetic: '/eɡ/',
        example: 'I eat eggs for breakfast.',
        topicId: 'food',
      },
      {
        id: 'milk',
        english: 'Milk',
        vietnamese: 'Sữa',
        phonetic: '/mɪlk/',
        example: 'Milk is good for children.',
        topicId: 'food',
      },
      {
        id: 'coffee',
        english: 'Coffee',
        vietnamese: 'Cà phê',
        phonetic: '/ˈkɔːfi/',
        example: 'Vietnamese coffee is famous.',
        topicId: 'food',
      },
      {
        id: 'tea',
        english: 'Tea',
        vietnamese: 'Trà',
        phonetic: '/tiː/',
        example: 'Green tea is healthy.',
        topicId: 'food',
      },
    ],
  },
  {
    id: 'body',
    name: 'Body Parts',
    nameVietnamese: 'Bộ Phận Cơ Thể',
    description: 'Học từ vựng về các bộ phận trên cơ thể',
    icon: '👤',
    color: '#E17055',
    words: [
      {
        id: 'head',
        english: 'Head',
        vietnamese: 'Đầu',
        phonetic: '/hed/',
        example: 'My head hurts.',
        topicId: 'body',
      },
      {
        id: 'face',
        english: 'Face',
        vietnamese: 'Mặt',
        phonetic: '/feɪs/',
        example: 'She has a beautiful face.',
        topicId: 'body',
      },
      {
        id: 'eye',
        english: 'Eye',
        vietnamese: 'Mắt',
        phonetic: '/aɪ/',
        example: 'Her eyes are blue.',
        topicId: 'body',
      },
      {
        id: 'nose',
        english: 'Nose',
        vietnamese: 'Mũi',
        phonetic: '/noʊz/',
        example: 'I can smell with my nose.',
        topicId: 'body',
      },
      {
        id: 'mouth',
        english: 'Mouth',
        vietnamese: 'Miệng',
        phonetic: '/maʊθ/',
        example: 'Open your mouth.',
        topicId: 'body',
      },
      {
        id: 'ear',
        english: 'Ear',
        vietnamese: 'Tai',
        phonetic: '/ɪr/',
        example: 'I can hear with my ears.',
        topicId: 'body',
      },
      {
        id: 'hand',
        english: 'Hand',
        vietnamese: 'Tay',
        phonetic: '/hænd/',
        example: 'Wash your hands.',
        topicId: 'body',
      },
      {
        id: 'foot',
        english: 'Foot',
        vietnamese: 'Chân',
        phonetic: '/fʊt/',
        example: 'My foot is tired.',
        topicId: 'body',
      },
      {
        id: 'arm',
        english: 'Arm',
        vietnamese: 'Cánh tay',
        phonetic: '/ɑːrm/',
        example: 'Raise your arm.',
        topicId: 'body',
      },
      {
        id: 'leg',
        english: 'Leg',
        vietnamese: 'Chân (từ hông xuống)',
        phonetic: '/leɡ/',
        example: 'My leg is strong.',
        topicId: 'body',
      },
      {
        id: 'finger',
        english: 'Finger',
        vietnamese: 'Ngón tay',
        phonetic: '/ˈfɪŋɡər/',
        example: 'I have ten fingers.',
        topicId: 'body',
      },
      {
        id: 'hair',
        english: 'Hair',
        vietnamese: 'Tóc',
        phonetic: '/her/',
        example: 'Her hair is long.',
        topicId: 'body',
      },
    ],
  },
  {
    id: 'school',
    name: 'School',
    nameVietnamese: 'Trường Học',
    description: 'Học từ vựng về trường học và học tập',
    icon: '🏫',
    color: '#6C5CE7',
    words: [
      {
        id: 'school',
        english: 'School',
        vietnamese: 'Trường học',
        phonetic: '/skuːl/',
        example: 'I go to school every day.',
        topicId: 'school',
      },
      {
        id: 'classroom',
        english: 'Classroom',
        vietnamese: 'Lớp học',
        phonetic: '/ˈklæsruːm/',
        example: 'The classroom is clean.',
        topicId: 'school',
      },
      {
        id: 'student',
        english: 'Student',
        vietnamese: 'Học sinh',
        phonetic: '/ˈstuːdənt/',
        example: 'She is a good student.',
        topicId: 'school',
      },
      {
        id: 'book',
        english: 'Book',
        vietnamese: 'Sách',
        phonetic: '/bʊk/',
        example: 'I read a book.',
        topicId: 'school',
      },
      {
        id: 'pen',
        english: 'Pen',
        vietnamese: 'Bút',
        phonetic: '/pen/',
        example: 'I write with a pen.',
        topicId: 'school',
      },
      {
        id: 'pencil',
        english: 'Pencil',
        vietnamese: 'Bút chì',
        phonetic: '/ˈpensəl/',
        example: 'Draw with a pencil.',
        topicId: 'school',
      },
      {
        id: 'notebook',
        english: 'Notebook',
        vietnamese: 'Vở',
        phonetic: '/ˈnoʊtbʊk/',
        example: 'Write in your notebook.',
        topicId: 'school',
      },
      {
        id: 'desk',
        english: 'Desk',
        vietnamese: 'Bàn học',
        phonetic: '/desk/',
        example: 'Sit at your desk.',
        topicId: 'school',
      },
      {
        id: 'chair',
        english: 'Chair',
        vietnamese: 'Ghế',
        phonetic: '/tʃer/',
        example: 'The chair is comfortable.',
        topicId: 'school',
      },
      {
        id: 'blackboard',
        english: 'Blackboard',
        vietnamese: 'Bảng đen',
        phonetic: '/ˈblækbɔːrd/',
        example: 'Write on the blackboard.',
        topicId: 'school',
      },
      {
        id: 'homework',
        english: 'Homework',
        vietnamese: 'Bài tập về nhà',
        phonetic: '/ˈhoʊmwɜːrk/',
        example: 'Do your homework.',
        topicId: 'school',
      },
      {
        id: 'exam',
        english: 'Exam',
        vietnamese: 'Bài kiểm tra',
        phonetic: '/ɪɡˈzæm/',
        example: 'The exam is difficult.',
        topicId: 'school',
      },
    ],
  },
  {
    id: 'weather',
    name: 'Weather',
    nameVietnamese: 'Thời Tiết',
    description: 'Học từ vựng về thời tiết và khí hậu',
    icon: '🌤️',
    color: '#00B894',
    words: [
      {
        id: 'sunny',
        english: 'Sunny',
        vietnamese: 'Nắng',
        phonetic: '/ˈsʌni/',
        example: 'Today is sunny.',
        topicId: 'weather',
      },
      {
        id: 'rainy',
        english: 'Rainy',
        vietnamese: 'Mưa',
        phonetic: '/ˈreɪni/',
        example: 'It is rainy today.',
        topicId: 'weather',
      },
      {
        id: 'cloudy',
        english: 'Cloudy',
        vietnamese: 'Có mây',
        phonetic: '/ˈklaʊdi/',
        example: 'The sky is cloudy.',
        topicId: 'weather',
      },
      {
        id: 'windy',
        english: 'Windy',
        vietnamese: 'Có gió',
        phonetic: '/ˈwɪndi/',
        example: 'It is windy outside.',
        topicId: 'weather',
      },
      {
        id: 'hot',
        english: 'Hot',
        vietnamese: 'Nóng',
        phonetic: '/hɑːt/',
        example: 'Summer is hot.',
        topicId: 'weather',
      },
      {
        id: 'cold',
        english: 'Cold',
        vietnamese: 'Lạnh',
        phonetic: '/koʊld/',
        example: 'Winter is cold.',
        topicId: 'weather',
      },
      {
        id: 'warm',
        english: 'Warm',
        vietnamese: 'Ấm',
        phonetic: '/wɔːrm/',
        example: 'Spring is warm.',
        topicId: 'weather',
      },
      {
        id: 'cool',
        english: 'Cool',
        vietnamese: 'Mát',
        phonetic: '/kuːl/',
        example: 'Autumn is cool.',
        topicId: 'weather',
      },
      {
        id: 'snow',
        english: 'Snow',
        vietnamese: 'Tuyết',
        phonetic: '/snoʊ/',
        example: 'Snow is white.',
        topicId: 'weather',
      },
      {
        id: 'storm',
        english: 'Storm',
        vietnamese: 'Bão',
        phonetic: '/stɔːrm/',
        example: 'The storm is dangerous.',
        topicId: 'weather',
      },
      {
        id: 'thunder',
        english: 'Thunder',
        vietnamese: 'Sấm',
        phonetic: '/ˈθʌndər/',
        example: 'I hear thunder.',
        topicId: 'weather',
      },
      {
        id: 'lightning',
        english: 'Lightning',
        vietnamese: 'Chớp',
        phonetic: '/ˈlaɪtnɪŋ/',
        example: 'Lightning is bright.',
        topicId: 'weather',
      },
    ],
  },
];
