import React from 'react';
import { FlatList, View, Text, StyleSheet } from 'react-native';
import { useHideTabBarOnScroll } from './useHideTabBarOnScroll';

// Component đơn giản để test
export const SimpleScrollableScreen = () => {
  const { onScroll } = useHideTabBarOnScroll({ scrollThreshold: 30 });

  // Tạo dữ liệu mẫu
  const data = Array.from({ length: 100 }, (_, index) => ({
    id: index.toString(),
    title: `Item ${index + 1}`,
    subtitle: `This is subtitle for item ${index + 1}`,
  }));

  const renderItem = ({ item }: { item: any }) => (
    <View style={styles.item}>
      <Text style={styles.title}>{item.title}</Text>
      <Text style={styles.subtitle}>{item.subtitle}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        onScroll={onScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  listContent: {
    paddingBottom: 100, // Để tránh bị che bởi tab bar
  },
  item: {
    backgroundColor: 'white',
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
});
