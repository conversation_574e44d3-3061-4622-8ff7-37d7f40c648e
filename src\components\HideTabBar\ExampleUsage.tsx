import React from 'react';
import { FlatList, View, Text, StyleSheet } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/Ionicons';
import { BottomTabWrapper, AnimatedTabBar, useHideTabBarOnScroll } from './index';

const Tab = createBottomTabNavigator();

// Screen với FlatList
const HomeScreen = () => {
  const { onScroll } = useHideTabBarOnScroll();

  const data = Array.from({ length: 50 }, (_, i) => ({ 
    id: i, 
    title: `Item ${i}`,
    description: `Description for item ${i}`
  }));

  const renderItem = ({ item }: { item: any }) => (
    <View style={styles.listItem}>
      <Text style={styles.itemTitle}>{item.title}</Text>
      <Text style={styles.itemDescription}>{item.description}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        onScroll={onScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

// Screen khác
const ProfileScreen = () => {
  const { onScroll } = useHideTabBarOnScroll();

  const data = Array.from({ length: 30 }, (_, i) => ({ 
    id: i, 
    title: `Profile Item ${i}`,
    subtitle: `Subtitle ${i}`
  }));

  const renderItem = ({ item }: { item: any }) => (
    <View style={styles.profileItem}>
      <Text style={styles.profileTitle}>{item.title}</Text>
      <Text style={styles.profileSubtitle}>{item.subtitle}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        onScroll={onScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

// Main App component
export const ExampleApp = () => {
  return (
    <NavigationContainer>
      <BottomTabWrapper>
        <Tab.Navigator
          tabBar={(props) => <AnimatedTabBar {...props} />}
          screenOptions={{
            headerShown: false,
          }}
        >
          <Tab.Screen 
            name="Home" 
            component={HomeScreen}
            options={{
              tabBarIcon: ({ color, size }) => (
                <Icon name="home-outline" size={size} color={color} />
              ),
            }}
          />
          <Tab.Screen 
            name="Profile" 
            component={ProfileScreen}
            options={{
              tabBarIcon: ({ color, size }) => (
                <Icon name="person-outline" size={size} color={color} />
              ),
            }}
          />
          <Tab.Screen 
            name="Settings" 
            component={ProfileScreen} // Reuse for demo
            options={{
              tabBarIcon: ({ color, size }) => (
                <Icon name="settings-outline" size={size} color={color} />
              ),
            }}
          />
        </Tab.Navigator>
      </BottomTabWrapper>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listItem: {
    backgroundColor: 'white',
    padding: 20,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  profileItem: {
    backgroundColor: 'white',
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  profileTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  profileSubtitle: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
});
