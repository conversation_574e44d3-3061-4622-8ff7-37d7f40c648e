import React, { createContext, useRef } from "react";
import { Animated } from "react-native";
import { TabBarContextType, TabBarProviderProps, AnimationConfig } from "./types";

export const TabBarVisibilityContext = createContext<
  TabBarContextType | undefined
>(undefined);

interface BottomTabWrapperProps extends TabBarProviderProps {
  animationConfig?: AnimationConfig;
}

export const BottomTabWrapper: React.FC<BottomTabWrapperProps> = ({
  children,
  animationConfig = {},
}) => {
  const {
    duration = 200,
    hideValue = 100,
    showValue = 0,
  } = animationConfig;

  const tabBarTranslateY = useRef(new Animated.Value(showValue)).current;

  const slideTabBar = (direction: "up" | "down") => {
    Animated.timing(tabBarTranslateY, {
      toValue: direction === "up" ? showValue : hideValue,
      duration,
      useNativeDriver: true,
    }).start();
  };

  return (
    <TabBarVisibilityContext.Provider
      value={{
        tabBarTranslateY,
        slideTabBar,
      }}
    >
      {children}
    </TabBarVisibilityContext.Provider>
  );
};
