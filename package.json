{"name": "TuVungMoiNgay", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-camera-roll/camera-roll": "^7.4.0", "@react-native-community/push-notification-ios": "^1.11.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@tanstack/react-query": "^5.18.1", "axios": "^1.6.7", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.77.1", "react-native-linear-gradient": "^2.8.3", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.16.7", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "^4.9.0", "react-native-size-matters": "^0.4.2", "react-native-tts": "^4.1.1", "react-native-vector-icons": "^10.2.0", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/eslint-config": "0.77.1", "@react-native/metro-config": "0.77.1", "@react-native/typescript-config": "0.77.1", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.3.1", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}